package org.biosino.lf.mash.mashweb.controller;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.page.TableDataInfo;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.biosino.lf.mash.mashweb.dto.*;
import org.biosino.lf.mash.mashweb.service.CacheService;
import org.biosino.lf.mash.mashweb.service.DiversityService;
import org.biosino.lf.mash.mashweb.util.DownloadUtils;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.biosino.lf.mash.mashweb.vo.SelectItemVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2025/7/21
 */
@RestController
@RequestMapping("/diversity")
@RequiredArgsConstructor
public class DiversityController extends BaseController {

    private final DiversityService diversityService;

    @RequestMapping("/biogeography")
    public AjaxResult createBiogeography(@RequestBody BiogeographyCreateDTO paramsDTO) {
        String id = diversityService.createBiogeography(paramsDTO);
        return AjaxResult.success(id);
    }

    @RequestMapping("/getSpeciesNameList")
    public AjaxResult getSpeciesNameList(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        List<String> list = diversityService.getSpeciesNameList(queryDTO);
        return AjaxResult.success(list);
    }

    @RequestMapping("/speciesDiversity")
    public AjaxResult createDiversity(@RequestBody DiversityCompareCreateDTO paramsDTO) {
        String id = diversityService.createDiversity(paramsDTO);
        return AjaxResult.success(id);
    }

    @RequestMapping("/getTableResult")
    public TableDataInfo getTableResult(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        Page<SamplesBioGeographyVO> result = diversityService.getTableResult(queryDTO);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

    @RequestMapping("/getSpeciesName")
    public AjaxResult getSpeciesName(@RequestParam("domain") String domain,
                                     @RequestParam("level") String level,
                                     SelectQueryDTO query) {
        List<String> collect = CacheService.domainLevelSpecieNames.get(domain + level);
        if (StrUtil.isNotBlank(query.getSearch())) {
            collect = collect.stream().filter(x -> StrUtil.containsAnyIgnoreCase(x, query.getSearch())).toList();
        }
        collect = collect.stream().limit(100).toList();
        List<SelectItemVO> list = new ArrayList<>();
        collect.forEach((x) -> {
            SelectItemVO itemVO = new SelectItemVO();
            itemVO.setValue(x);
            itemVO.setLabel(x);
            list.add(itemVO);
        });
        return AjaxResult.success(list);
    }

    @RequestMapping("/getResultMapData")
    public AjaxResult getResultMapData(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        MapDataInfoDTO result = diversityService.getMapData(queryDTO);
        return AjaxResult.success(result);
    }

    @RequestMapping("/barplot")
    public AjaxResult barplot(BiogeographyResultQueryDTO queryDTO) {
        Object result = diversityService.barPlotData(queryDTO);
        return AjaxResult.success(result);
    }

    @RequestMapping("/treeMap")
    public AjaxResult treeMap(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        Object result = diversityService.treeMapData(queryDTO);
        return AjaxResult.success(result);
    }

    @RequestMapping("/pcoa")
    public String pcoa(BiogeographyResultQueryDTO queryDTO) {
        return diversityService.pcoa(queryDTO);
    }

    @RequestMapping("/downloadChart")
    @ResponseBody
    public void downloadChart(BiogeographyResultQueryDTO param, HttpServletRequest request, HttpServletResponse response) throws IOException {
        File file = diversityService.getChartFile(param);
        DownloadUtils.download(request, response, file);
    }
}
