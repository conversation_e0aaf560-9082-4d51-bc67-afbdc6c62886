suppressMessages(library(GetoptLong))
suppressMessages(library(pheatmap))
suppressMessages(library(tidyverse))

rm(list = ls(all.names = TRUE))

# 参数定义
GetoptLong(
  "log10_file=s",        "log10 KO 丰度文件（Top_30_KO_Abundance.tsv）",
  "abundance_file=s",    "原始平均丰度文件（MASH.KO_Sample.wide.average.tsv）",
  "annotation_file=s",   "KO 注释文件（Top_30_KO_Gene_Path.csv，含 KO 和 Type 列）",
  "Group_file=s",        "样本分组文件，包含 sample_name 和 Group 两列（大小写不敏感）",
  "out_DIR=s",           "输出目录",
  "prefix=s",            "输出文件名前缀",
  "verbose!",              "是否打印日志"
)

if (verbose) {
  cat("参数信息:\n")
  cat("log10_file:", log10_file, "\n")
  cat("abundance_file:", abundance_file, "\n")
  cat("annotation_file:", annotation_file, "\n")
  cat("Group_file:", Group_file, "\n")
  cat("out_DIR:", out_DIR, "\n")
  cat("prefix:", prefix, "\n")
}

if (!dir.exists(out_DIR)) dir.create(out_DIR, recursive = TRUE)

# 读取数据
log10_mat <- read.delim(log10_file, row.names = 1, check.names = FALSE)
avg_mat_all <- read.delim(abundance_file, row.names = 1, check.names = FALSE)
anno_df <- read.csv(annotation_file, stringsAsFactors = FALSE)
group_df <- read.delim(Group_file, stringsAsFactors = FALSE)

# 处理 group_df 列名统一为小写
colnames(group_df) <- tolower(colnames(group_df))

# 检查必要列
if (!all(c("sample_name", "group") %in% colnames(group_df))) {
  stop("Group_file 必须包含 sample_name 和 Group 两列（不区分大小写）")
}

# 检查 annotation_file 列
if (!all(c("KO", "Type") %in% colnames(anno_df))) {
  stop("annotation_file 必须包含 KO 和 Type 列")
}

# 标准化样本名，去除前缀等
clean_names <- function(x) gsub("^.*?_", "", x)
colnames(log10_mat) <- clean_names(colnames(log10_mat))
colnames(avg_mat_all) <- clean_names(colnames(avg_mat_all))
group_df$sample_name <- clean_names(group_df$sample_name)

# 对齐样本，保留分组信息中有的样本
common_samples <- intersect(group_df$sample_name, colnames(log10_mat))
group_df <- group_df %>% filter(sample_name %in% common_samples)
log10_mat <- log10_mat[, group_df$sample_name, drop = FALSE]
avg_mat_all <- avg_mat_all[, group_df$sample_name, drop = FALSE]

# 提取 top 30 KO
top30_KOs <- anno_df$KO
log10_mat <- log10_mat[top30_KOs, , drop = FALSE]
avg_mat <- avg_mat_all[top30_KOs, , drop = FALSE]

# 行注释（KO 类型）
annotation_row <- data.frame(Type = factor(anno_df$Type, levels = c("selected", "other")))
rownames(annotation_row) <- anno_df$KO

# 字体颜色
font_colors <- ifelse(anno_df$Type == "selected", "#377EB8", "grey50")
names(font_colors) <- anno_df$KO

# 列注释（样本分组）
annotation_col <- data.frame(Group = factor(group_df$group))
rownames(annotation_col) <- group_df$sample_name

# 生成分组颜色，支持多个组或单个组
group_levels <- unique(group_df$group)
if(length(group_levels) == 1) {
  group_colors <- setNames("#1f77b4", group_levels)
} else {
  group_colors <- RColorBrewer::brewer.pal(min(length(group_levels), 9), "Set1")
  names(group_colors) <- group_levels
}

annotation_colors <- list(
  Type = c(selected = "#377EB8", other = "grey70"),
  Group = group_colors
)

# 热图颜色
colors_heatmap <- colorRampPalette(c("#4575B4", "white", "#D73027"))(100)

# 热图绘图函数
draw_KO_heatmap <- function(mat, title, out_png, out_pdf) {
  if (nrow(mat) == 0) {
    warning("矩阵为空，跳过绘图: ", title)
    return()
  }

  breaks <- seq(min(mat, na.rm = TRUE), max(mat, na.rm = TRUE), length.out = 101)

  p <- pheatmap(mat,
                annotation_row = annotation_row,
                annotation_col = annotation_col,
                annotation_colors = annotation_colors,
                color = colors_heatmap,
                breaks = breaks,
                cluster_rows = TRUE,
                cluster_cols = FALSE,
                show_rownames = TRUE,
                show_colnames = TRUE,
                cellwidth = 8,
                cellheight = 8,
                fontsize_row = 6,
                fontsize_col = 8,
                labels_row_color = font_colors,
                main = title,
                silent = TRUE)

  png(out_png, width = 8, height = 6, units = "in", res = 150)
  print(p)
  dev.off()

  pdf(out_pdf, width = 8, height = 6)
  print(p)
  dev.off()
}

# 输出路径
out_log10_png <- file.path(out_DIR, paste0(prefix, "_KO_log10_heatmap.png"))
out_log10_pdf <- file.path(out_DIR, paste0(prefix, "_KO_log10_heatmap.pdf"))
out_avg_png   <- file.path(out_DIR, paste0(prefix, "_KO_average_heatmap.png"))
out_avg_pdf   <- file.path(out_DIR, paste0(prefix, "_KO_average_heatmap.pdf"))

# 添加这一段：导出输入数据
write.csv(log10_mat, file = file.path(out_DIR, paste0(prefix, "_KO_log10_heatmap_input.csv")))
write.csv(avg_mat,   file = file.path(out_DIR, paste0(prefix, "_KO_average_heatmap_input.csv")))

# 绘图
draw_KO_heatmap(log10_mat, "Top 30 KO log10 abundance", out_log10_png, out_log10_pdf)
draw_KO_heatmap(avg_mat,   "Top 30 KO average abundance", out_avg_png, out_avg_pdf)

if (verbose) cat("KO 热图绘制完成。\n")

