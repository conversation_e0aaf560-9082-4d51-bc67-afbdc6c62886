import request from '@/utils/request';

const baseURL = '/diversity';

// 创建biogeography任务
export function createBiogeography(data) {
  return request({
    url: `${baseURL}/biogeography`,
    method: 'post',
    data: data,
  });
}

// 获取物种名称列表（用于下拉框）
export function getSpeciesName(params) {
  return request({
    url: `${baseURL}/getSpeciesName`,
    method: 'get',
    params: params,
  });
}

// 获取物种名称列表（用于地图下拉框）
export function getSpeciesNameList(data) {
  return request({
    url: `${baseURL}/getSpeciesNameList`,
    method: 'post',
    data: data,
  });
}

// 获取地图数据
export function getResultMapData(data) {
  return request({
    url: `${baseURL}/getResultMapData`,
    method: 'post',
    data: data,
  });
}

// 获取表格结果
export function getTableResult(data) {
  return request({
    url: `${baseURL}/getTableResult`,
    method: 'post',
    data: data,
  });
}

// 创建Species Diversity任务
export function createSpeciesDiversity(data) {
  return request({
    url: `${baseURL}/speciesDiversity`,
    method: 'post',
    data: data,
  });
}

export function getBarPlotData(params) {
  return request({
    url: `${baseURL}/barplot`,
    method: 'get',
    params: params,
  });
}
