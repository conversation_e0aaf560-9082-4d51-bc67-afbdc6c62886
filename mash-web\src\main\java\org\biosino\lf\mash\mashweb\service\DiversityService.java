package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.text.csv.*;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.FileProperties;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.*;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.biosino.lf.mash.mashweb.mongo.repository.SamplesRepository;
import org.biosino.lf.mash.mashweb.util.MathUtils;
import org.biosino.lf.mash.mashweb.util.RuntimeUtils;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2025/7/21
 */
@RequiredArgsConstructor
@Service
public class DiversityService {

    private final SamplesRepository samplesRepository;
    private final FileProperties fileProperties;

    private final static String[] GROUP_COLOR = {
            "#377EB8",
            "#FF7F00",
            "#E41A1C",
            "#4DAF4A",
            "#984EA3"
    };

    public String createBiogeography(BiogeographyCreateDTO paramsDTO) {
        String id = IdUtil.getSnowflakeNextIdStr();

        // 先生成输入文件
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(id));
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(id));

        // 将数据写入input目录
        File speciesNameFile = FileUtil.writeUtf8Lines(paramsDTO.getSpeciesNames(), FileUtil.file(inputDir, "species_name_list.txt"));
        // 如果是条件，就查询
        List<String> selectRunIds;
        if (CollUtil.isNotEmpty(paramsDTO.getRunIds())) {
            selectRunIds = paramsDTO.getRunIds();
        } else {
            SamplesQueryDTO queryDTO = paramsDTO.getQueryDTO();
            selectRunIds = samplesRepository.findDistinctRunId(queryDTO);
        }
        File sampleListFile = FileUtil.writeUtf8Lines(selectRunIds, FileUtil.file(inputDir, "sample_list.txt"));


        String abundanceFilePath = fileProperties.getBaseDataDir() + "/Abundance_Table/" + paramsDTO.getDomain() + "." + paramsDTO.getLevel() + ".percent.csv";

        String cmd = StrUtil.format("python {} {} {} {} {}",
                fileProperties.getScriptDir() + "/process_species_data.py",
                abundanceFilePath,
                speciesNameFile.getAbsolutePath(),
                sampleListFile.getAbsolutePath(),
                outputDir.getAbsolutePath()
        );

        int exitCode = RuntimeUtils.execForExitCode(cmd, id);
        if (exitCode != 0) {
            throw new ServiceException("分析失败");
        }
        // 开始写入结果文件，方便后续
        for (String speciesName : paramsDTO.getSpeciesNames()) {
            File file = FileUtil.file(outputDir, speciesName + ".tsv");
            if (!file.exists()) {
                continue;
            }
            CsvReader reader = CsvUtil.getReader(CsvReadConfig.defaultConfig().setFieldSeparator('\t'));
            List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);

            // 查询数据
            List<String> runIds = list.stream().map(SamplesBioGeographyVO::getRunId).toList();


            List<Samples> samples = samplesRepository.findByRunIdIn(runIds);

            // 转成runId to Sample Map
            Map<String, Samples> runIdToSampleMap = samples.stream().collect(Collectors.toMap(Samples::getRunId, Function.identity(), (x, y) -> y));

            List<SamplesBioGeographyVO> result = list.stream().peek(x -> {
                if (runIdToSampleMap.containsKey(x.getRunId())) {
                    Samples item = runIdToSampleMap.get(x.getRunId());
                    // BeanUtil.copyProperties(item, x);
                    if (item.getLatitude() != null) {
                        x.setLatitude(String.valueOf(item.getLatitude()));
                    }
                    if (item.getLongitude() != null) {
                        x.setLongitude(String.valueOf(item.getLongitude()));
                    }
                }
            }).toList();

            // 将结果写入到结果文件
            File resultFile = FileUtil.file(outputDir, speciesName + "_result.csv");
            CsvWriter writer = CsvUtil.getWriter(FileUtil.getWriter(resultFile, Charset.defaultCharset(), false));
            writer.writeBeans(result);
            writer.close();
        }

        return id;
    }

    // 根据id 查询结果数据
    public Page<SamplesBioGeographyVO> getTableResult(BiogeographyResultQueryDTO queryDTO) {
        // 读取结果文件中的所有run_list
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(queryDTO.getTaskId()));
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(queryDTO.getTaskId()));
        File file;
        CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
        if (StrUtil.isNotBlank(queryDTO.getSpeciesName())) {
            file = FileUtil.file(outputDir, queryDTO.getSpeciesName() + "_result.csv");
        } else {
            file = FileUtil.file(inputDir, "Group.txt");
            csvReadConfig.setFieldSeparator('\t');
        }
        if (!file.exists()) {
            return null;
        }

        CsvReader reader = CsvUtil.getReader(csvReadConfig);
        List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);

        // 使用hutool进行内存分页
        Pageable pageable = queryDTO.getPageable();
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), list.size());

        List<SamplesBioGeographyVO> pageContent = CollUtil.sub(list, start, end);

        // 查询其他的数据
        List<String> runIds = pageContent.stream().map(SamplesBioGeographyVO::getRunId).toList();
        Map<String, Samples> runIdToSampleMap = samplesRepository.findByRunIdIn(runIds).stream().collect(Collectors.toMap(Samples::getRunId, Function.identity(), (x, y) -> y));

        List<SamplesBioGeographyVO> result = pageContent.stream().peek(x -> {
            if (runIdToSampleMap.containsKey(x.getRunId())) {
                Samples item = runIdToSampleMap.get(x.getRunId());
                BeanUtil.copyProperties(item, x);
            }
        }).toList();

        return new PageImpl<>(result, pageable, list.size());
    }


    public MapDataInfoDTO getMapData(BiogeographyResultQueryDTO queryDTO) {
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(queryDTO.getTaskId()));
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(queryDTO.getTaskId()));

        File file = FileUtil.file(outputDir, queryDTO.getSpeciesName() + "_result.csv");

        if (!file.exists()) {
            return null;
        }

        File sampleListFile = FileUtil.file(inputDir, "sample_list.txt");
        List<String> sampleList = FileUtil.readUtf8Lines(sampleListFile);


        CsvReader reader = CsvUtil.getReader();
        List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);
        Map<String, List<SamplesBioGeographyVO>> collect = list.stream()
                .collect(Collectors.groupingBy(x -> (x.getLongitude() != null ? x.getLongitude() : "-") + StrPool.TAB + (x.getLatitude() != null ? x.getLatitude() : "-")));

        List<MapDataDTO> mapData = new ArrayList<>();
        collect.forEach((k, v) -> {
            String[] split = k.split(StrPool.TAB);
            String longitude = split[0];
            String latitude = split[1];
            List<SamplesBioGeographyVO> dtos = v.stream().toList();
            mapData.add(new MapDataDTO(!longitude.equals("-") ? Double.parseDouble(longitude) : null,
                    !latitude.equals("-") ? Double.parseDouble(latitude) : null, dtos.stream().map(SamplesBioGeographyVO::getValue).max(Comparator.comparing(Double::doubleValue)).orElse(0.0), v.size()));
        });
        MapDataInfoDTO result = new MapDataInfoDTO();
        // 求所有的 Min Medium Max
        List<Double> avgData = list.stream().map(x -> x.getValue() / 10000).sorted(Comparator.comparingDouble(Double::doubleValue)).toList();
        double min = avgData.get(0);
        double max = avgData.get(avgData.size() - 1);
        double median = MathUtils.calculateMedian(avgData);
        result.setMapData(mapData);
        result.setMin(String.valueOf(min));
        result.setMax(String.valueOf(max));
        result.setMedian(String.valueOf(median));

        result.setSelectedNum(sampleList.size());
        result.setDetectedNum(list.size());
        return result;
    }

    public List<String> getSpeciesNameList(BiogeographyResultQueryDTO queryDTO) {
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(queryDTO.getTaskId()));
        File sampleListFile = FileUtil.file(inputDir, "species_name_list.txt");

        return FileUtil.readUtf8Lines(sampleListFile);
    }

    public String createDiversity(DiversityCompareCreateDTO paramsDTO) {
        String id = IdUtil.getSnowflakeNextIdStr();

        // 先生成输入文件
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(id));
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(id));

        File groupFile = FileUtil.file(inputDir, "Group.txt");
        File groupColorFile = FileUtil.file(inputDir, "group_color.tsv");
        List<String> groupInfo = new ArrayList<>();
        groupInfo.add("sample_name\tgroup");
        List<String> groupColor = new ArrayList<>();
        groupColor.add("Group\tColor");
        int i = 0;

        List<String> runIds = new ArrayList<>();
        for (GroupInfoDTO groupInfoDTO : paramsDTO.getGroupInfos()) {
            runIds.addAll(groupInfoDTO.getRunIds());
            for (String runId : groupInfoDTO.getRunIds()) {
                groupInfo.add(StrUtil.format("{}\t{}", runId, groupInfoDTO.getGroupName()));
            }
            groupColor.add(StrUtil.format("{}\t{}", groupInfoDTO.getGroupName(), GROUP_COLOR[i]));
            i++;
        }

        // 拷贝文件
        runIds.forEach(x -> {
            File sourceFile = FileUtil.file(fileProperties.getBaseDataDir(), "bracken_16_species", StrUtil.format("{}.k2_pluspf_16gb_20220908_bracken_species.report", x));
            File targetFile = FileUtil.file(inputDir, "bracken_16_species", StrUtil.format("{}.k2_pluspf_16gb_20220908_bracken_species.report", x));
            FileUtil.copy(sourceFile, targetFile, true);
        });

        FileUtil.writeUtf8Lines(groupInfo, groupFile);
        FileUtil.writeUtf8Lines(groupColor, groupColorFile);

        FileUtil.mkdir(outputDir);

        String cmd = StrUtil.format("{} {} {} {} {} {}",
                fileProperties.getScriptDir() + "/mash_code/run.kraken.cmd",
                fileProperties.getScriptDir() + "/mash_code",
                inputDir.getAbsolutePath(),
                outputDir.getAbsolutePath(),
                paramsDTO.getDomain(),
                paramsDTO.getLevel()
        );

        int exitCode = RuntimeUtils.execForExitCode(cmd);
        if (exitCode != 0) {
            throw new ServiceException("分析失败");
        }
        return id;
    }

    public Object barPlotData(BiogeographyResultQueryDTO queryDTO) {
        String taskId = queryDTO.getTaskId();
        String domain = queryDTO.getDomain();
        String taxonomy = queryDTO.getLevel();
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(taskId));

        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File groupFile = FileUtil.file(inputDir, "Group.txt");
        List<List<String>> groupFileLines = readLinesToListStr(groupFile, '\t');
        List<String> groups = groupFileLines.stream().map(x -> x.get(1)).distinct().toList();

        File file = FileUtil.file(outputDir, "Barplot_Table", StrUtil.format("Barplot.{}.{}.csv", domain, taxonomy));
        File legendFile = FileUtil.file(outputDir, "Barplot_Table", StrUtil.format("Barlegend.{}.{}.csv", domain, taxonomy));

        List<List<String>> dataList = readLinesToListStr(file);
        List<List<String>> sortedList = dataList.stream().sorted(Comparator.comparing(x -> x.get(0))).toList();
        Map<String, List<List<String>>> map = sortedList.stream().collect(Collectors.groupingBy(x -> x.get(1), LinkedHashMap::new, Collectors.toList()));
        Set<String> group = map.keySet();
        JSONObject result = new JSONObject();
        JSONObject data = new JSONObject();
        for (String g : group) {
            List<List<String>> list = map.get(g);
            LinkedHashMap<String, List<List<String>>> taxMap = list.stream().collect(Collectors.groupingBy(x -> x.get(2), LinkedHashMap::new, Collectors.toList()));

            data.set(g, taxMap);
        }
        result.set("groups", groups);
        result.set("data", MapUtil.sort(data));
        result.set("legend", readLinesToListStr(legendFile).stream().map(x -> x.get(0)).collect(Collectors.toList()));

        return result;
    }


    public static List<List<String>> readLinesToListStr(File file, Boolean containsHeader, char fieldSeparator) {
        if (!file.exists()) {
            throw new ServiceException("读取" + file.getAbsolutePath() + "文件不存在");
        }
        CsvReadConfig config = new CsvReadConfig();
        // containsHeader为true不读取第一行
        config.setContainsHeader(containsHeader);
        config.setFieldSeparator(fieldSeparator);
        config.setSkipEmptyRows(true);
        CsvReader reader = CsvUtil.getReader(config);
        CsvData read = reader.read(ResourceUtil.getUtf8Reader(file.getAbsolutePath()));
        List<CsvRow> rows = read.getRows();
        List<List<String>> dataList = new ArrayList<>();
        for (CsvRow row : rows) {
            List<String> strings = row.getRawList();
            dataList.add(strings);
        }
        return dataList;
    }

    public static List<List<String>> readLinesToListStr(File file) {
        return readLinesToListStr(file, true, ',');
    }

    public static List<List<String>> readLinesToListStr(File file, char fieldSeparator) {
        return readLinesToListStr(file, true, fieldSeparator);
    }

    public String pcoa(BiogeographyResultQueryDTO queryDTO) {
        String taskId = queryDTO.getTaskId();
        String domain = queryDTO.getDomain();
        String taxonomy = queryDTO.getLevel();
        String betaMethod = queryDTO.getBetaMethod();

        String fileName = StrUtil.format("PCoA.{}.{}.{}.html", domain, taxonomy, betaMethod);
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File file = FileUtil.file(outputDir, "PCoA_Figure", fileName);
        return FileUtil.readUtf8String(file);
    }

    public File getChartFile(BiogeographyResultQueryDTO queryDTO) {
        String taskId = queryDTO.getTaskId();
        String domain = queryDTO.getDomain();
        String level = queryDTO.getLevel();
        String betaMethod = queryDTO.getBetaMethod();
        String group = queryDTO.getGroup();

        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File file = null;


        switch (queryDTO.getChartNo()) {
            case 1:
                file = FileUtil.file(outputDir, "Barplot_Figure",
                        StrUtil.format("Barplot.{}.{}.pdf", domain, level));
                break;
            case 2:
                file = FileUtil.file(outputDir, "Lefse_Figure", StrUtil.format("Lefse.{}.{}.pdf", domain, level));
                break;
            case 3:
                file = FileUtil.file(outputDir, "Treemap_Figure",
                        StrUtil.format("Treemap_{}_Top14.pdf", group));
                break;
            case 4:
                file = FileUtil.file(outputDir, "PCoA_Figure",
                        StrUtil.format("PCoA.{}.{}.{}.pdf", domain, level, betaMethod));
                break;
            default:
                break;
        }
        return file;
    }

    public Object treeMapData(BiogeographyResultQueryDTO queryDTO) {
        String taskId = queryDTO.getTaskId();
        String group = queryDTO.getGroup();
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File file = FileUtil.file(outputDir, "Treemap_Figure",
                StrUtil.format("Treemap_{}_Top14_data.csv", group));
        List<List<String>> result = readLinesToListStr(file);
        return result;
    }
}
