suppressMessages(library(GetoptLong))
suppressMessages(library(dplyr))

rm(list = ls(all.names = TRUE))

packages = c("dplyr")
ipak <- function(pkg){
  new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
  if(length(new.pkg))
    install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.edu.cn/CRAN/")
  sapply(pkg, require, character.only = TRUE)
  cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)

GetoptLong(
  "user_Sample_in_file=s", "Input normalized KO abundance matrix file (TSV)",
  "Group_file=s", "Sample group information file (TSV)",
  "KO_list_exist=s", "Whether KO_list exists: Yes or No",
  "out_DIR=s", "Output directory",
  "prefix=s", "Prefix for output files",
  "verbose!", "Print verbose messages"
)

if(!dir.exists(out_DIR)){
  dir.create(out_DIR, recursive = TRUE)
}

cat("读取输入文件...\n")
data_mat <- read.csv(user_Sample_in_file, sep = "\t", header = TRUE, row.names = 1, check.names = FALSE)
colnames(data_mat) <- gsub("^MASH_", "", colnames(data_mat))

group_df <- read.csv(Group_file, sep = "\t", header = TRUE, check.names = FALSE)

if(!all(c("sample_name", "Group") %in% colnames(group_df))){
  colnames(group_df)[1:2] <- c("sample_name", "Group")
}

if(!all(c("sample_name", "Group") %in% colnames(group_df))){
  stop("Group_file必须包含'sample_name'和'Group'列")
}

common_samples <- intersect(colnames(data_mat), group_df$sample_name)
if(length(common_samples) < 2){
  stop("表达矩阵和Group文件中交集样本少于2，无法进行差异分析")
}
data_mat <- data_mat[, common_samples, drop = FALSE]
group_df <- group_df[group_df$sample_name %in% common_samples, ]
group_df <- group_df[match(colnames(data_mat), group_df$sample_name), ]

n_groups <- length(unique(group_df$Group))
if(n_groups < 2){
  stop("组数少于2，无法进行差异分析")
}

groups <- unique(group_df$Group)
pval_list <- list()

for(i in 1:(length(groups)-1)){
  for(j in (i+1):length(groups)){
    grp1 <- groups[i]
    grp2 <- groups[j]

    samples_grp1 <- group_df$sample_name[group_df$Group == grp1]
    samples_grp2 <- group_df$sample_name[group_df$Group == grp2]

    pvals <- apply(data_mat, 1, function(x){
      wilcox.test(x[samples_grp1], x[samples_grp2])$p.value
    })

    pval_list[[paste0(grp1, "_vs_", grp2, "_pvalue")]] <- pvals
  }
}

pval_df <- do.call(cbind, pval_list)
pval_df <- as.data.frame(pval_df)
pval_df$KO <- rownames(data_mat)
pval_df <- pval_df[, c("KO", setdiff(colnames(pval_df), "KO"))]

first_p_col <- colnames(pval_df)[2]
pval_df <- pval_df[order(pval_df[[first_p_col]]), ]

KO_list <- NULL
if(tolower(KO_list_exist) == "yes"){
  KO_list <- tryCatch({
    ko_tmp <- readLines("input/KO.list")
    unique(ko_tmp)
  }, error = function(e){
    cat("读取KO.list失败，忽略KO_list\n")
    NULL
  })
}

top30_df <- NULL
if(!is.null(KO_list)){
  ko_in_data <- KO_list[KO_list %in% pval_df$KO]
  top30_df <- pval_df[pval_df$KO %in% ko_in_data, ]
  if(nrow(top30_df) < 30){
    n_need <- 30 - nrow(top30_df)
    other_ko <- setdiff(pval_df$KO, ko_in_data)
    supplement_df <- pval_df[pval_df$KO %in% other_ko, ][1:n_need, ]
    top30_df <- rbind(top30_df, supplement_df)
  }
  top30_df <- top30_df[1:min(30, nrow(top30_df)), ]
} else {
  top30_df <- pval_df[1:min(30, nrow(pval_df)), ]
}

# 读取KO_Gene_Path映射文件，严格按三列读取，不拆分
ko_gp_path <- "input/KO_Gene_Path.tsv"
ko_anno <- tryCatch({
  read.delim(ko_gp_path, header = TRUE, sep = "\t", stringsAsFactors = FALSE, check.names = FALSE, quote = "", fill = TRUE)
}, error = function(e){
  stop("无法读取 KO_Gene_Path.tsv 文件，请确保文件存在且格式正确")
})

# 创建一个基于KO的映射表（Gene和Pathway均为字符串，保持完整）
ko_map <- ko_anno %>% 
  select(KO, Genes, PathwayIDs) %>%
  rename(Gene = Genes, Pathway = PathwayIDs)

# 给差异结果表添加Type列：属于KO.list为selected，不属于为other
pval_df$Type <- if(!is.null(KO_list)){
  ifelse(pval_df$KO %in% KO_list, "selected", "other")
} else {
  "other"
}

top30_df$Type <- if(!is.null(KO_list)){
  ifelse(top30_df$KO %in% KO_list, "selected", "other")
} else {
  "other"
}

# 给差异结果表添加Gene和Pathway列（左连接）
pval_df <- left_join(pval_df, ko_map, by = "KO")
top30_df <- left_join(top30_df, ko_map, by = "KO")

# 写出结果，保持Gene和Pathway列字符串完整
write.csv(pval_df, file = file.path(out_DIR, paste0(prefix, "_diff_results.csv")), row.names = FALSE, quote = TRUE)
write.csv(top30_df, file = file.path(out_DIR, paste0(prefix, "_top30_diff_results.csv")), row.names = FALSE, quote = TRUE)

cat("差异分析完成，结果已保存。\n")

