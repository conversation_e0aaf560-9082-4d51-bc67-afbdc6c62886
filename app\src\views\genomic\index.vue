<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <el-tabs v-model="form.analysisType">
              <el-tab-pane label="Biogeography" name="Biogeography">
                <el-form
                  ref="biogeographyFormRef"
                  :model="form"
                  :rules="biogeographyRules"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <el-form-item label="Genes Input Type">
                    <el-radio-group v-model="form.geneType">
                      <el-radio value="Gene Name">Gene Name</el-radio>
                      <el-radio value="Orthology Entry"
                        >Orthology Entry
                      </el-radio>
                      <el-radio value="Pathway-KO">Pathway-KO</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <!-- Species Section -->
                  <el-form-item
                    v-if="form.geneType === 'Gene Name'"
                    label="Genes Name"
                    prop="geneName"
                    class="mb-2"
                  >
                    <el-select
                      v-model="form.geneName"
                      :teleported="false"
                      filterable
                      multiple
                      placeholder="Select genes names"
                      class="w-100"
                      :max-collapse-tags="3"
                      clearable
                      :multiple-limit="10"
                    >
                      <el-option
                        v-for="it in geneNameOpt"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                    <div class="species-warning">
                      The maximum number of queried ko is 10
                    </div>
                  </el-form-item>
                  <el-form-item
                    v-else-if="form.geneType === 'Orthology Entry'"
                    label="KO ID"
                    prop="koList"
                    class="mb-2"
                  >
                    <el-input
                      v-model="form.koList"
                      type="textarea"
                      placeholder="Enter KO ID (one per line)"
                      raws="6"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-else
                    label="Pathway Name"
                    prop="pathwayKO"
                    class="mb-2"
                  >
                    <el-select
                      v-model="form.pathwayKO"
                      :teleported="false"
                      filterable
                      clearable
                      placeholder="Select pathway"
                      class="w-100"
                    >
                      <el-option
                        v-for="it in pathwayNameOpt"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                  </el-form-item>

                  <!-- Data Select  -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label mt-1">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Data Select
                      </div>
                    </template>
                    <!-- Biogeography Mode -->
                    <div class="data-filter-section">
                      <el-form-item label="Longitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLongitude"
                          range
                          :max="180.0"
                          :min="-180.0"
                          :step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.longitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.longitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="Latitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLatitude"
                          range
                          :max="90"
                          :min="-90"
                          :step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.latitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.latitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item
                        label="Water Body Type"
                        prop="waterBodyType"
                        class="mb-2"
                      >
                        <el-cascader
                          v-model="form.waterBodyType"
                          :popper-append-to-body="false"
                          :options="waterBodyOpt"
                          :props="props"
                          class="w-100"
                          placeholder="Select"
                          @change="handleWaterBodyTypeChange"
                        />
                      </el-form-item>

                      <el-form-item
                        class="mb-2"
                        label="Or Select Data from Cart"
                        prop="selectedGroup"
                      >
                        <el-select
                          v-model="form.selectedGroup"
                          :teleported="false"
                          placeholder="Select a group"
                          clearable
                        >
                          <el-option
                            v-for="it in groupOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          />
                        </el-select>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      :loading="submitLoading"
                      @click="submitBiogeography"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="Function Analysis" name="Function Analysis">
                <el-form
                  ref="functionAnalysisFormRef"
                  :model="form"
                  :rules="functionAnalysisRules"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <el-form-item label="Genes Input Type" prop="geneType">
                    <el-radio-group v-model="form.geneType">
                      <el-radio value="Gene Name">Gene Name</el-radio>
                      <el-radio value="Orthology Entry"
                        >Orthology Entry
                      </el-radio>
                      <el-radio value="Pathway-KO">Pathway-KO</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <!-- Gene Name Section -->
                  <el-form-item
                    v-if="form.geneType === 'Gene Name'"
                    label="Genes Name"
                    class="mb-2"
                    prop="geneName"
                  >
                    <el-select
                      v-model="form.geneName"
                      :teleported="false"
                      multiple
                      filterable
                      placeholder="Select genes names"
                      class="w-100"
                      :max-collapse-tags="3"
                    >
                      <el-option
                        v-for="it in geneNameOpt"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                  </el-form-item>
                  <!-- KO ID Section -->
                  <el-form-item
                    v-else-if="form.geneType === 'Orthology Entry'"
                    label="KO ID"
                    class="mb-2"
                    prop="koList"
                  >
                    <el-input
                      v-model="form.koList"
                      type="textarea"
                      placeholder="Enter KO ID (one per line)"
                      rows="6"
                    ></el-input>
                  </el-form-item>
                  <!-- Pathway Name Section -->
                  <el-form-item
                    v-else
                    label="Pathway Name"
                    class="mb-2"
                    prop="pathwayKO"
                  >
                    <el-select
                      v-model="form.pathwayKO"
                      :teleported="false"
                      filterable
                      clearable
                      placeholder="Select pathway"
                      class="w-100"
                    >
                      <el-option
                        v-for="it in pathwayNameOpt"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item>
                    <template #label>
                      <div class="section-label mt-1">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Data Select
                      </div>
                    </template>
                    <div class="data-filter-section">
                      <el-form-item prop="type">
                        <el-radio-group v-model="form.type">
                          <el-radio value="From Biota">From Biota</el-radio>
                          <el-radio value="From Cart">From Cart</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <div
                        v-for="(group, index) in form.functionGroups"
                        :key="group.id"
                        class="diversity-group mb-3"
                      >
                        <div
                          class="group-header d-flex justify-space-between align-items-center mb-2"
                        >
                          <el-input
                            v-model="group.name"
                            class="group-title-input"
                            size="small"
                            :style="{ width: '120px' }"
                          />
                          <div class="group-actions">
                            <el-button
                              v-if="index === form.functionGroups.length - 1"
                              type="success"
                              size="small"
                              round
                              :icon="Plus"
                              @click="addGroup"
                            >
                            </el-button>
                            <el-button
                              v-if="form.functionGroups.length > 1"
                              type="danger"
                              size="small"
                              round
                              :icon="Minus"
                              @click="removeGroup(index)"
                            >
                            </el-button>
                          </div>
                        </div>

                        <div class="group-content">
                          <el-form-item
                            v-if="form.type === 'From Biota'"
                            class="mb-2"
                            label="Water Body Type"
                          >
                            <el-cascader
                              v-model="group.waterBodyType"
                              :options="waterBodyOpt"
                              :props="props"
                              class="w-100"
                              placeholder="Select"
                              @change="handleWaterBodyTypeChange"
                            />
                          </el-form-item>
                          <el-form-item
                            v-else
                            class="mb-2"
                            label="Select Data from Cart"
                          >
                            <el-select
                              v-model="group.selectedGroup"
                              :teleported="false"
                              placeholder="Select"
                            >
                              <el-option
                                v-for="it in groupOptions"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                              />
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </div>
                  </el-form-item>

                  <!-- 隐藏的验证字段用于验证分组 -->
                  <el-form-item prop="functionGroups" style="display: none">
                    <el-input v-model="form.functionGroups" />
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      :loading="submitLoading"
                      @click="submitFunctionAnalysis"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
        <el-col :span="18">
          <!-- Biogeography 结果显示区域 -->
          <div
            v-show="form.analysisType === 'Biogeography'"
            class="card mb-1 pos-relative"
          >
            <!-- 有结果时显示地图和数据 -->
            <div v-show="hasResults">
              <div class="d-flex justify-space-between align-items-center">
                <h3 class="mb-0 mt-0">
                  <span class="mr-05 font-600"
                    >Global distribution and relativate abundance of</span
                  >

                  <el-select
                    v-model="summarySelect"
                    :teleported="false"
                    filterable
                    placeholder="Select genes names"
                    class="mr-1"
                    style="width: 736px"
                    @change="onSummarySelectChange"
                  >
                    <el-option
                      v-for="ko in koList"
                      :key="ko"
                      :label="ko"
                      :value="ko"
                    />
                  </el-select>
                </h3>
              </div>
              <el-divider class="mt-1"></el-divider>
              <div
                class="map-container"
                :class="{ fullscreen: isMapFullscreen }"
              >
                <div
                  id="genomicMap"
                  style="width: 100%; height: 560px; background-color: #fffff5"
                ></div>
                <!-- 全屏切换按钮 -->
                <el-button
                  class="fullscreen-btn"
                  type="primary"
                  :icon="isMapFullscreen ? 'FullScreen' : 'Rank'"
                  circle
                  :title="isMapFullscreen ? '退出全屏' : '全屏显示'"
                  @click="toggleMapFullscreen"
                >
                </el-button>
              </div>
              <div class="chart-card">
                <!-- 检测/选择样本信息 -->
                <div class="sample-info">
                  <span>Detected/Selected Samples:</span>
                  <span class="sample-count"
                    >{{ sampleStatistics.detectedSamples }}/{{
                      sampleStatistics.selectedSamples
                    }}</span
                  >
                </div>

                <!-- 标准化丰度统计 -->
                <div class="sample-info">
                  <div>Normalized Abundance (Min/Median/MAX):</div>
                  <div class="sample-count">
                    {{ sampleStatistics.min }}/{{ sampleStatistics.median }}/{{
                      sampleStatistics.max
                    }}
                  </div>
                </div>

                <!-- 图例圆圈 -->
                <div class="legend-container">
                  <div class="legend-item">
                    <div class="size-label">0.0001%</div>
                    <div class="circle" style="width: 7px; height: 7px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.001%</div>
                    <div class="circle" style="width: 11px; height: 11px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.01%</div>
                    <div class="circle" style="width: 17px; height: 17px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.1%</div>
                    <div class="circle" style="width: 21px; height: 21px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">1%</div>
                    <div class="circle" style="width: 25px; height: 25px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">10%</div>
                    <div class="circle" style="width: 30px; height: 30px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">100%</div>
                    <div class="circle" style="width: 35px; height: 35px"></div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 无结果时显示空状态 -->
            <div v-show="!hasResults">
              <el-empty
                description="Please select parameters from the left panel and submit to view results"
                :image-size="200"
              >
                <template #image>
                  <el-icon size="100" color="#c0c4cc">
                    <DataAnalysis />
                  </el-icon>
                </template>
              </el-empty>
            </div>
          </div>
          <div
            v-show="form.analysisType === 'Function Analysis'"
            class="card mb-1"
          >
            <div>
              <img
                src="@/assets/images/hotMap.png"
                style="width: 100%"
                alt=""
              />
            </div>

            <el-table
              :data="analysisData"
              style="width: 100%"
              border
              :header-cell-style="{
                backgroundColor: '#F1F5F9',
                color: '#333333',
                fontWeight: 700,
              }"
            >
              <el-table-column label="KO" prop="ko" width="120">
                <template #default="scope">
                  <a>
                    {{ scope.row.ko }}
                  </a>
                </template>
              </el-table-column>

              <el-table-column
                label="Depth"
                prop="depth"
                width="120"
              ></el-table-column>
              <el-table-column
                label="P-value"
                prop="pValue"
                width="120"
              ></el-table-column>

              <el-table-column label="Pathway" prop="pathway" min-width="200">
                <template #default="scope">
                  <div class="pathway-container">
                    <span
                      v-for="(path, index) in scope.row.pathwayList"
                      :key="index"
                      class="pathway-item"
                    >
                      <a
                        class="link-style"
                        :href="getPathwayUrl(path)"
                        target="_blank"
                      >
                        {{ path }}
                      </a>
                      <span v-if="index < scope.row.pathwayList.length - 1"
                        >,
                      </span>
                    </span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div v-show="hasResults" class="card">
            <!-- 有结果时显示表格 -->
            <div>
              <div class="d-flex justify-space-between align-items-center">
                <h3 class="mb-0 mt-0">Sample List</h3>
                <el-popover
                  placement="bottom-end"
                  :width="300"
                  trigger="hover"
                  popper-class="metadata-popover"
                >
                  <template #reference>
                    <el-button type="success"> Add other metadata</el-button>
                  </template>
                  <div class="metadata-selector">
                    <h4 class="metadata-title">Select Columns to Display</h4>
                    <div class="column-checkboxes">
                      <el-checkbox
                        v-for="column in allColumns"
                        :key="column.prop"
                        v-model="column.visible"
                        :label="column.label"
                        class="column-checkbox"
                      />
                    </div>
                  </div>
                </el-popover>
              </div>
              <el-divider class="mt-1"></el-divider>
              <el-table
                ref="table"
                tooltip-effect="dark"
                :data="dataTable"
                :header-cell-style="{
                  backgroundColor: '#F1F5F9',
                  color: '#333333',
                  fontWeight: 700,
                }"
                border
                :stripe="true"
              >
                <!-- Default visible columns -->
                <el-table-column
                  v-if="getColumnVisibility('runId')"
                  label="Run ID"
                  prop="runId"
                  width="200"
                >
                  <template #default="scope">
                    <div class="d-flex">
                      <el-tag
                        v-if="scope.row.group && !isBiogeographyAnalysis"
                        effect="dark"
                        round
                        :type="getGroupTagType(scope.row.group)"
                        size="small"
                        class="mr-05"
                      >
                        {{ scope.row.group }}
                      </el-tag>
                      {{ scope.row.runId }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('bioProjectId')"
                  label="BioProject ID"
                  prop="bioProjectId"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('latitude')"
                  label="Latitude"
                  prop="latitude"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('longitude')"
                  label="Longitude"
                  prop="longitude"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('hydrosphereType')"
                  label="Hydrosphere Type"
                  prop="hydrosphereType"
                  width="160"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('geolocation')"
                  label="Geolocation"
                  prop="waterBodyTypeByGeographic"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('waterBodyType')"
                  label="Water Body Type"
                  prop="waterBodyTypeByClassification"
                  width="160"
                ></el-table-column>

                <!-- Hidden columns that can be toggled -->
                <el-table-column
                  v-if="getColumnVisibility('depth')"
                  label="Depth"
                  prop="depth"
                  width="100"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('temperature')"
                  label="Temperature"
                  prop="temperature"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('salinity')"
                  label="Salinity"
                  prop="salinity"
                  width="100"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('ph')"
                  label="pH"
                  prop="ph"
                  width="80"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('criticalZone')"
                  label="Critical Zone"
                  prop="criticalZone"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('samplingSubstrate')"
                  label="Sampling Substrate"
                  prop="samplingSubstrate"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('country')"
                  label="Country"
                  prop="country"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('waterBodyName')"
                  label="Water Body Name"
                  prop="waterBodyName"
                  width="160"
                ></el-table-column>

                <!-- Analysis Results column (always visible) -->
                <el-table-column
                  v-if="getColumnVisibility('analysisResults')"
                  label="Analysis Results"
                  prop="analysisResults"
                  width="160"
                  align="center"
                >
                  <template #default="scope">
                    <router-link :to="`/sample/detail/${scope.row.runId}`">
                      <div class="text-primary">View</div>
                    </router-link>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                :total="total"
                class="mb-1"
                :auto-scroll="false"
                @pagination="getDataList(true)"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>

  <BrowseCart />
</template>

<script setup>
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import {
    DataAnalysis,
    Menu,
    Minus,
    Plus,
    Promotion,
  } from '@element-plus/icons-vue';
  import L from 'leaflet';
  import 'leaflet/dist/leaflet.css';
  import BrowseCart from '@/components/ShoppingCart/BrowseCart.vue';
  import {
    createBiogeography,
    createFunctionAnalysis,
    getKoList,
    getPathKoToNameList,
    getPathwayName,
    getResultMapData,
    getTableResult,
  } from '@/api/genomic';
  import { getWaterBodyTypeByHydrosphere } from '@/api/samples';
  import axios from 'axios';
  import { useCartStore } from '@/store/modules/cart';

  const { proxy } = getCurrentInstance();
  const cartStore = useCartStore();
  const table = ref(null);
  const biogeographyFormRef = ref(null);
  const functionAnalysisFormRef = ref(null);

  // 查询参数和分页
  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
  });
  const total = ref(0);

  // 表单验证规则
  const biogeographyRules = ref({
    geneName: [
      {
        required: true,
        message: 'Please select at least one gene name',
        trigger: 'change',
      },
    ],
    koList: [
      { required: true, message: 'Please enter KO ID', trigger: 'blur' },
    ],
    pathwayKO: [
      { required: true, message: 'Please select a pathway', trigger: 'change' },
    ],
    // 自定义验证：Water Body Type 和 Selected Group 必须选择其中一个
    waterBodyType: [{ validator: validateDataSelection, trigger: 'change' }],
    selectedGroup: [{ validator: validateDataSelection, trigger: 'change' }],
  });

  // 自定义验证函数：确保 Water Body Type 或 Selected Group 至少选择一个
  function validateDataSelection(rule, value, callback) {
    if (!form.value.waterBodyType || form.value.waterBodyType.length === 0) {
      if (!form.value.selectedGroup) {
        callback(
          new Error(
            'Please select either Water Body Type or a Group from Cart',
          ),
        );
        return;
      }
    }
    callback();
  }

  // Function Analysis分组验证函数
  function validateFunctionGroups(rule, value, callback) {
    if (form.value.functionGroups.length === 0) {
      callback(new Error('Please add at least one group'));
      return;
    }

    for (let i = 0; i < form.value.functionGroups.length; i++) {
      const group = form.value.functionGroups[i];

      if (!group.name || group.name.trim() === '') {
        callback(new Error(`Please enter a name for Group ${i + 1}`));
        return;
      }

      if (form.value.type === 'From Biota') {
        if (!group.waterBodyType || group.waterBodyType.length === 0) {
          callback(
            new Error(`Please select Water Body Type for ${group.name}`),
          );
          return;
        }
      } else if (form.value.type === 'From Cart') {
        if (!group.selectedGroup) {
          callback(
            new Error(`Please select a group from cart for ${group.name}`),
          );
          return;
        }
      }
    }
    callback();
  }

  // Function Analysis验证规则
  const functionAnalysisRules = ref({
    geneType: [
      {
        required: true,
        message: 'Please select genes input type',
        trigger: 'change',
      },
    ],
    geneName: [
      {
        required: true,
        message: 'Please select at least one gene name',
        trigger: 'change',
      },
    ],
    koList: [
      { required: true, message: 'Please enter KO ID', trigger: 'blur' },
    ],
    pathwayKO: [
      { required: true, message: 'Please select a pathway', trigger: 'change' },
    ],
    type: [
      {
        required: true,
        message: 'Please select data source type',
        trigger: 'change',
      },
    ],
    functionGroups: [{ validator: validateFunctionGroups, trigger: 'change' }],
  });

  // 添加地理数据的ref
  const oceanData = ref(null);
  const lakesData = ref(null);
  const riversData = ref(null);
  const geoDataLoading = ref(true);

  // biogeography相关数据
  const biogeographyData = ref({
    submitLoading: false,
    taskId: '',
    koList: [],
    summarySelect: '',
    geneNameOpt: [],
    pathwayNameOpt: [],
    hasResults: false, // 是否有结果数据
  });

  const {
    submitLoading,
    taskId,
    koList,
    summarySelect,
    geneNameOpt,
    pathwayNameOpt,
    hasResults,
  } = toRefs(biogeographyData.value);

  // 从购物车store获取分组选项
  const groupOptions = computed(() => {
    return cartStore.cartGroups.map(group => ({
      label: `${group.name} (${group.runCount} runs)`,
      value: group.name, // 使用group名称作为value，在提交时再获取对应的runIds
    }));
  });

  // 判断是否为biogeography分析
  const isBiogeographyAnalysis = computed(() => {
    return form.value.analysisType === 'Biogeography';
  });

  const dataTable = ref([]);

  const form = ref({
    analysisType: 'Biogeography',
    geneType: 'Gene Name',
    koList: '',
    geneName: [],
    pathwayKO: '',
    sliderLongitude: [-180.0, 180.0],
    sliderLatitude: [-90, 90],
    longitudeTo: -180.0,
    longitudeFrom: 180.0,
    latitudeTo: -90,
    latitudeFrom: 90,
    waterBodyType: [],
    geolocation: '',
    type: 'From Cart',
    selectedGroup: '', // 改为单选，存储选中的group名称
    functionGroups: [
      {
        id: 1,
        name: 'Group A',
        waterBodyType: [],
        selectedGroup: '',
      },
    ],
  });

  // 监听滑动条变化，同步到输入框
  watch(
    () => form.value.sliderLongitude,
    newVal => {
      form.value.longitudeTo = newVal[0];
      form.value.longitudeFrom = newVal[1];
    },
  );

  watch(
    () => form.value.sliderLatitude,
    newVal => {
      form.value.latitudeTo = newVal[0];
      form.value.latitudeFrom = newVal[1];
    },
  );

  // 监听输入框变化，同步到滑动条
  watch(
    () => [form.value.longitudeTo, form.value.longitudeFrom],
    newVal => {
      form.value.sliderLongitude = [newVal[0], newVal[1]];
    },
  );

  watch(
    () => [form.value.latitudeTo, form.value.latitudeFrom],
    newVal => {
      form.value.sliderLatitude = [newVal[0], newVal[1]];
    },
  );

  // 监听selectedGroup变化，提供用户反馈
  watch(
    () => form.value.selectedGroup,
    (newVal, oldVal) => {
      if (newVal && !oldVal) {
        // 选中了group
        console.log(`Selected group: ${newVal}`);
      } else if (!newVal && oldVal) {
        // 清除了group选择
        console.log('Cleared group selection, will use filter conditions');
      }
    },
  );

  // 监听hasResults变化，当显示结果时重新渲染地图
  watch(
    () => hasResults.value,
    newVal => {
      if (newVal) {
        // 使用nextTick确保DOM更新完成后再重新渲染地图
        nextTick(() => {
          setTimeout(() => {
            if (window.mapInstance) {
              window.mapInstance.invalidateSize();
            }
          }, 100); // 给一点延迟确保容器完全显示
        });
      }
    },
  );

  // 处理级联选择器变化
  function handleWaterBodyTypeChange(value) {
    console.log('Water body type changed:', value);
    if (value && value.length > 0) {
      const category = value[0]; // 一级选择
      // 当用户选择一级选项时，加载对应的二级选项
      loadSecondLevelOptions(category);
    }
  }

  // 加载水体类型级联选择器数据
  function loadWaterBodyTypeOptions() {
    console.log('Loading water body type options...');
    // 初始化时加载所有分类的二级选项
    loadSecondLevelOptions('water_body_type_by_geographic');
    loadSecondLevelOptions('water_body_type_by_classification');
  }

  // 加载指定分类的二级选项
  function loadSecondLevelOptions(category) {
    console.log('Loading second level options for category:', category);

    getWaterBodyTypeByHydrosphere({
      hydrosphereType: 'All',
      category: category,
    })
      .then(response => {
        console.log(`${category} response:`, response);
        if (response.data && response.data.length) {
          const options = response.data.map(item => ({
            label: item,
            value: item,
          }));

          // 找到对应的一级选项并更新其children
          const targetIndex =
            category === 'water_body_type_by_geographic' ? 0 : 1;
          waterBodyOpt.value[targetIndex].children = options;
          console.log(`Updated ${category} options:`, options);
        } else {
          console.log(`No ${category} data received`);
        }
      })
      .catch(error => {
        console.error(`Failed to load ${category} options:`, error);
      });
  }

  // 动态加载的水体类型选项，参考首页实现
  const waterBodyOpt = ref([
    {
      value: 'water_body_type_by_geographic',
      label: 'Geolocation',
      children: [],
    },
    {
      value: 'water_body_type_by_classification',
      label: 'Water Body Type',
      children: [],
    },
  ]);
  const props = {
    expandTrigger: 'hover',
  };

  // 获取分组标签类型
  function getGroupTagType(group) {
    const groupTypes = {
      'Group A': 'primary',
      'Group B': 'warning',
      'Group C': 'success',
      'Group D': 'info',
      'Group E': 'danger',
    };
    return groupTypes[group] || 'info';
  }

  const isMapFullscreen = ref(false);

  const analysisData = ref([
    {
      ko: 'ko:K00001',
      depth: '8.5085',
      pathway: 'map00010;map00011',
      pValue: '0.03',
      pathwayList: ['ko01230', 'map00010'],
    },
    {
      ko: 'ko:K00002',
      depth: '7.2341',
      pValue: '0.03',
      pathway: 'map00020;map00030;map00040',
      pathwayList: ['ko02060', 'map00520'],
    },
    {
      ko: 'ko:K00003',
      depth: '9.1256',
      pValue: '1',

      pathway: 'map00050',
      pathwayList: ['ko02060', 'map00050'],
    },
  ]);

  // 触发Function Analysis分组验证
  function triggerFunctionGroupValidation() {
    if (functionAnalysisFormRef.value) {
      functionAnalysisFormRef.value.validateField('functionGroups');
    }
  }

  // 添加分组
  function addGroup() {
    const newGroupId = form.value.functionGroups.length + 1;
    const groupLetter = String.fromCharCode(64 + newGroupId); // A, B, C, etc.

    form.value.functionGroups.push({
      id: newGroupId,
      name: `Group ${groupLetter}`,
      waterBodyType: [],
      selectedGroup: '',
    });

    // 触发验证
    triggerFunctionGroupValidation();
  }

  // 删除分组
  function removeGroup(index) {
    if (form.value.functionGroups.length > 1) {
      form.value.functionGroups.splice(index, 1);
      // 触发验证
      triggerFunctionGroupValidation();
    }
  }

  // 显示隐藏列
  const allColumns = ref([
    { prop: 'runId', label: 'Run ID', visible: true, default: true },
    {
      prop: 'bioProjectId',
      label: 'BioProject ID',
      visible: true,
      default: true,
    },
    { prop: 'latitude', label: 'Latitude', visible: true, default: true },
    { prop: 'longitude', label: 'Longitude', visible: true, default: true },
    {
      prop: 'hydrosphereType',
      label: 'Hydrosphere Type',
      visible: true,
      default: true,
    },
    { prop: 'geolocation', label: 'Geolocation', visible: true, default: true },
    {
      prop: 'waterBodyType',
      label: 'Water Body Type',
      visible: true,
      default: true,
    },
    {
      prop: 'analysisResults',
      label: 'Analysis Results',
      visible: true,
      default: true,
    },
    { prop: 'depth', label: 'Depth', visible: false, default: false },
    {
      prop: 'temperature',
      label: 'Temperature',
      visible: false,
      default: false,
    },
    { prop: 'salinity', label: 'Salinity', visible: false, default: false },
    { prop: 'ph', label: 'pH', visible: false, default: false },
    {
      prop: 'criticalZone',
      label: 'Critical Zone',
      visible: false,
      default: false,
    },
    {
      prop: 'samplingSubstrate',
      label: 'Sampling Substrate',
      visible: false,
      default: false,
    },
    { prop: 'country', label: 'Country', visible: false, default: false },
    {
      prop: 'waterBodyName',
      label: 'Water Body Name',
      visible: false,
      default: false,
    },
  ]);

  function getColumnVisibility(prop) {
    const column = allColumns.value.find(col => col.prop === prop);
    return column ? column.visible : false;
  }

  const initMap = id => {
    // 检查所有地理数据是否已加载
    if (!oceanData.value || !lakesData.value || !riversData.value) {
      console.warn('地理数据尚未加载完成，等待加载...');
      return null;
    }

    var latlng = L.latLng(30, 0);

    var map = L.map(id, {
      center: latlng,
      zoom: 2,
      minZoom: 2,
      maxZoom: 18,
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    map.createPane('oceanPane');
    map.createPane('riverPane');
    map.createPane('pointsPane');

    map.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });

    L.geoJSON(oceanData.value, {
      onEachFeature: function (feature, layer) {
        let labelLatLng;
        // 根据特征名称选择标签位置
        if (feature.properties.name === 'North Pacific Ocean') {
          labelLatLng = L.latLng(30, -150);
        } else if (feature.properties.name === 'South Pacific Ocean') {
          labelLatLng = L.latLng(-30, -140);
        } else {
          // 默认使用中心点
          labelLatLng = layer.getBounds().getCenter();
        }

        // 创建一个标记
        var label = L.marker(labelLatLng, {
          icon: L.divIcon({
            className: 'ocean-label',
            html: feature.properties.name,
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
      },

      style: function () {
        return {
          fillColor: '#1C4F80', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // var lakeLayer = null;
    L.geoJSON(lakesData.value, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let zoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: zoom > 4 ? feature.properties.Name : '',
            }),
          );
        });
      },

      style: function () {
        return {
          fillColor: '#9ABAE7', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    L.geoJSON(riversData.value, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let riverZoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: riverZoom > 4 ? feature.properties.name : '',
            }),
          );
        });
      },
      style: function () {
        return {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          pane: 'riverPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // 保存地图实例到全局变量，用于全屏切换时调整大小
    window.mapInstance = map;
  };

  // 全屏切换方法
  function toggleMapFullscreen() {
    isMapFullscreen.value = !isMapFullscreen.value;
    nextTick(() => {
      setTimeout(() => {
        const mapElement = document.getElementById('genomicMap');
        if (mapElement && window.mapInstance) {
          window.mapInstance.invalidateSize();
        }
      }, 300);
    });
  }

  function getSize(abundance) {
    var size = null;
    if (abundance < 0.0001 || (abundance >= 0.0001 && abundance < 0.001)) {
      size = 4;
    }
    if (abundance >= 0.001 && abundance < 0.01) {
      size = 6;
    }
    if (abundance >= 0.01 && abundance < 0.1) {
      size = 8;
    }
    if (abundance >= 0.1 && abundance < 1) {
      size = 10;
    }
    if (abundance >= 1 && abundance < 10) {
      size = 12;
    }
    if (abundance >= 10 && abundance < 100) {
      size = 14;
    }
    if (abundance >= 100) {
      size = 16;
    }
    return size;
  }

  function getPathwayUrl(path) {
    if (path.startsWith('ko')) {
      return `https://www.genome.jp/entry/${path}`;
    } else {
      return `https://www.kegg.jp/entry/${path}`;
    }
  }

  // 异步获取地理数据的函数
  async function fetchGeoData() {
    try {
      geoDataLoading.value = true;
      // 获取基础路径
      const basePath = import.meta.env.VITE_APP_PUBLIC_PATH || '/';

      const [oceanResponse, lakesResponse, riversResponse] = await Promise.all([
        axios.get(`${basePath}/geojson/ocean.json`),
        axios.get(`${basePath}/geojson/sample_lakes.json`),
        axios.get(`${basePath}/geojson/sample_rivers.json`),
      ]);

      oceanData.value = oceanResponse.data;
      lakesData.value = lakesResponse.data;
      riversData.value = riversResponse.data;
      return true;
    } catch (error) {
      console.error('加载地理数据失败:', error);
      return false;
    } finally {
      geoDataLoading.value = false;
    }
  }

  // 获取Gene Name下拉框数据
  function fetchGeneNameOptions() {
    getPathKoToNameList()
      .then(response => {
        geneNameOpt.value = response.data || [];
      })
      .catch(error => {
        console.error('获取Gene Name选项失败:', error);
      });
  }

  // 获取Pathway Name下拉框数据
  function fetchPathwayNameOptions() {
    getPathwayName()
      .then(response => {
        pathwayNameOpt.value = response.data || [];
      })
      .catch(error => {
        console.error('获取Pathway Name选项失败:', error);
      });
  }

  // 监听geneType变化，获取对应的下拉框数据
  watch(
    () => form.value.geneType,
    (newType, oldType) => {
      // 当geneType发生变化时，清空相关字段
      if (oldType && newType !== oldType) {
        form.value.geneName = [];
        form.value.koList = '';
        form.value.pathwayKO = '';
      }

      if (newType === 'Gene Name') {
        fetchGeneNameOptions();
      } else if (newType === 'Pathway-KO') {
        fetchPathwayNameOptions();
      }
    },
    { immediate: true },
  );

  // 提交biogeography任务
  function submitBiogeography() {
    // 表单验证
    if (!biogeographyFormRef.value) return;

    biogeographyFormRef.value.validate(valid => {
      if (!valid) {
        proxy.$modal.msgError('Please fill in all required fields correctly');
        return;
      }

      // 开始提交
      submitLoading.value = true;

      // 显示全局遮罩
      proxy.$modal.loading('Submitting analysis request...');

      // 构建提交参数
      const params = {
        kos:
          form.value.geneType === 'Gene Name'
            ? form.value.geneName
            : form.value.geneType === 'Orthology Entry'
              ? form.value.koList.split('\n').filter(ko => ko.trim())
              : [],
        pathway:
          form.value.geneType === 'Pathway-KO' ? form.value.pathwayKO : '',
      };

      // 根据是否选中group来决定提交的数据
      if (form.value.selectedGroup) {
        // 如果选中了group，提交group的runIds
        const selectedGroupData = cartStore.cartGroups.find(
          group => group.name === form.value.selectedGroup,
        );
        if (selectedGroupData) {
          params.runIds = selectedGroupData.runIds;
          // 不需要queryDTO，因为使用的是group的runIds
        } else {
          proxy.$modal.closeLoading();
          proxy.$modal.msgError('Selected group not found in cart');
          submitLoading.value = false;
          return;
        }
      } else {
        // 如果没有选中group，使用筛选条件
        params.runIds = [];
        params.queryDTO = {
          latitudeStart: form.value.latitudeTo,
          latitudeEnd: form.value.latitudeFrom,
          longitudeStart: form.value.longitudeTo,
          longitudeEnd: form.value.longitudeFrom,
        };

        // 根据一级下拉框的选择决定提交字段
        if (
          Array.isArray(form.value.waterBodyType) &&
          form.value.waterBodyType.length >= 2
        ) {
          const firstLevel = form.value.waterBodyType[0];
          const secondLevel = form.value.waterBodyType[1];

          if (firstLevel === 'water_body_type_by_geographic') {
            params.queryDTO.waterBodyTypeByGeographic = secondLevel;
          } else if (firstLevel === 'water_body_type_by_classification') {
            params.queryDTO.waterBodyTypeByClassification = secondLevel;
          }
        }
      }

      createBiogeography(params)
        .then(response => {
          taskId.value = response.msg;
          // 任务提交成功后，获取KO列表
          fetchKoList();
          proxy.$modal.msgSuccess('Analysis submitted successfully');
        })
        .catch(error => {
          console.error('提交任务失败:', error);
          proxy.$modal.msgError('Failed to submit analysis');
        })
        .finally(() => {
          proxy.$modal.closeLoading();
          submitLoading.value = false;
        });
    });
  }

  // 提交Function Analysis任务
  function submitFunctionAnalysis() {
    // 使用Element Plus表单验证
    functionAnalysisFormRef.value.validate(valid => {
      if (!valid) {
        proxy.$modal.msgError('Please fill in all required fields correctly');
        return;
      }

      // 开始提交
      submitLoading.value = true;
      proxy.$modal.loading('Submitting Function Analysis request...');

      // 构建提交参数
      const params = {
        kos:
          form.value.geneType === 'Gene Name'
            ? form.value.geneName
            : form.value.geneType === 'Orthology Entry'
              ? form.value.koList.split('\n').filter(ko => ko.trim())
              : [],
        pathway:
          form.value.geneType === 'Pathway-KO' ? form.value.pathwayKO : '',
        groupInfos: [],
      };

      // 构建分组信息
      form.value.functionGroups.forEach(group => {
        const groupInfo = {
          groupName: group.name.trim(),
        };

        if (form.value.type === 'From Biota') {
          // 根据级联选择器的值设置对应字段
          if (group.waterBodyType && group.waterBodyType.length >= 2) {
            const firstLevel = group.waterBodyType[0];
            const secondLevel = group.waterBodyType[1];

            if (firstLevel === 'water_body_type_by_geographic') {
              groupInfo.waterBodyTypeByGeographic = secondLevel;
            } else if (firstLevel === 'water_body_type_by_classification') {
              groupInfo.waterBodyTypeByClassification = secondLevel;
            }
          }
        } else if (form.value.type === 'From Cart') {
          // 从购物车获取runIds
          const selectedGroupData = cartStore.cartGroups.find(
            cartGroup => cartGroup.name === group.selectedGroup,
          );
          if (selectedGroupData) {
            groupInfo.runIds = selectedGroupData.runIds;
          }
        }

        params.groupInfos.push(groupInfo);
      });

      console.log('Submitting Function Analysis params:', params);

      createFunctionAnalysis(params)
        .then(response => {
          proxy.$modal.msgSuccess('Function Analysis submitted successfully');
          console.log('Function Analysis task ID:', response.msg);
          // 这里可以添加后续处理，比如跳转到结果页面或显示结果
        })
        .catch(error => {
          console.error('提交Function Analysis任务失败:', error);
          proxy.$modal.msgError('Failed to submit Function Analysis');
        })
        .finally(() => {
          proxy.$modal.closeLoading();
          submitLoading.value = false;
        });
    });
  }

  // 获取KO列表
  function fetchKoList() {
    if (!taskId.value) return;

    getKoList({ taskId: taskId.value })
      .then(response => {
        koList.value = response.data || [];

        // 默认选中第一个
        if (koList.value.length > 0) {
          summarySelect.value = koList.value[0];
          // 设置有结果状态
          hasResults.value = true;
          onSummarySelectChange();
        }
      })
      .catch(error => {
        console.error('获取KO列表失败:', error);
      });
  }

  // 结果下拉框选择变化时的处理
  function onSummarySelectChange() {
    if (summarySelect.value && taskId.value) {
      // 重置分页到第一页
      queryParams.value.pageNum = 1;
      fetchMapData();
      fetchTableData();
    }
  }

  // 获取地图数据
  function fetchMapData() {
    if (!taskId.value || !summarySelect.value) return;

    getResultMapData({
      taskId: taskId.value,
      ko: summarySelect.value,
    })
      .then(response => {
        const result = response.data;
        // 检查是否返回 null（没有分析结果）
        if (result) {
          // 正常处理数据
          const data = result || {};
          updateMapWithData(data.mapData || []);
          updateSampleStatistics(data);
        } else {
          proxy.$modal.confirm(`${summarySelect.value} no KO annotated`);
          // 显示空地图和空统计信息
          updateMapWithData([]);
          updateSampleStatistics({});
        }
      })
      .catch(error => {
        console.error('获取地图数据失败:', error);
      });
  }

  // 获取表格数据
  function fetchTableData() {
    if (!taskId.value || !summarySelect.value) return;

    getTableResult({
      taskId: taskId.value,
      ko: summarySelect.value,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    })
      .then(response => {
        const result = response.data;

        // 检查是否返回 null（没有分析结果）
        if (result) {
          dataTable.value = result.content;
          total.value = result.total || result.content?.length || 0;
        } else {
          dataTable.value = [];
          total.value = 0;
        }
      })
      .catch(error => {
        console.error('获取表格数据失败:', error);
      });
  }

  // 获取数据列表（用于分页）
  function getDataList(pageChange = false) {
    fetchTableData();
  }

  // 当前数据层引用
  let currentDataLayer = null;

  // 更新地图数据
  function updateMapWithData(mapData) {
    if (!window.mapInstance || !mapData || mapData.length === 0) {
      return;
    }

    // 清除之前的数据层
    if (currentDataLayer) {
      window.mapInstance.removeLayer(currentDataLayer);
    }

    // 创建canvas渲染器
    const canvasRenderer = L.canvas({ padding: 0.5 });

    // 创建图层组
    const pointsLayer = L.layerGroup();
    let markers = [];

    mapData.forEach(item => {
      if (item.latitude && item.longitude && item.number > 0) {
        // 根据value值计算圆点大小，使用提供的公式
        const value = item.size || item.number; // 使用size字段，如果没有则使用number
        const radius = Math.log10(value * 8) * 2.1;
        const finalRadius = Math.max(3, Math.min(35, radius)); // 限制最小和最大半径

        const circleMarker = L.circleMarker([item.latitude, item.longitude], {
          radius: finalRadius,
          fillColor: '#C6A5F4',
          color: '#C6A5F4',
          opacity: 1,
          weight: 0.5,
          fillOpacity: 1,
          pane: 'pointsPane',
          renderer: canvasRenderer,
        });

        // 添加弹出框显示详细信息
        const formattedLatitude = formatNumber(item.latitude);
        const formattedLongitude = formatNumber(item.longitude);
        const formattedMaxAbundance = formatNumber(value / 10000);

        circleMarker.bindPopup(`
          <div style="font-family: Arial, sans-serif; line-height: 1.4;">
            <div style="margin-bottom: 4px;">
              <strong>Sample Count:</strong> <span style="color: #E6A23C;">${item.number}</span>
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Latitude:</strong> ${formattedLatitude}°
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Longitude:</strong> ${formattedLongitude}°
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Max Abundance:</strong> ${formattedMaxAbundance}
            </div>
          </div>
        `);

        markers.push(circleMarker);
      }
    });

    // 添加到地图
    if (markers.length > 0) {
      pointsLayer.addLayer(L.featureGroup(markers));
      pointsLayer.addTo(window.mapInstance);
      currentDataLayer = pointsLayer;
    }
  }

  // 样本统计信息
  const sampleStatistics = ref({
    detectedSamples: 0,
    selectedSamples: 0,
    min: '',
    max: '',
    median: '',
  });

  // 数字格式化函数
  function formatNumber(number) {
    const decimalPart = number.toString().split('.')[1];
    if (decimalPart && decimalPart.length > 4) {
      return number.toFixed(4);
    } else {
      return number;
    }
  }

  // 更新样本统计信息
  function updateSampleStatistics(result) {
    sampleStatistics.value.detectedSamples = result.detectedNum || 0;
    sampleStatistics.value.selectedSamples = result.selectedNum || 0;
    sampleStatistics.value.min = formatNumber(parseFloat(result.min || 0));
    sampleStatistics.value.max = formatNumber(parseFloat(result.max || 0));
    sampleStatistics.value.median = formatNumber(
      parseFloat(result.median || 0),
    );
  }

  onMounted(async () => {
    await nextTick();

    // 加载水体类型选项
    loadWaterBodyTypeOptions();

    // 先获取地理数据，然后初始化地图
    const success = await fetchGeoData();
    if (success) {
      initMap('genomicMap');
    } else {
      console.error('地理数据加载失败，无法初始化地图');
    }
  });
</script>

<style lang="scss" scoped>
  .container-fluid {
    max-width: 1640px !important;
  }

  .submit-page {
    padding: 120px 0 45px 0;
  }

  :deep(.el-slider__bar),
  .filter-search {
    background-color: #1e7cb2;
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }

  :deep(.el-tabs__item) {
    font-size: 16px;
    font-weight: 600;

    &.is-active {
      color: #0080b0;
    }
  }

  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.el-upload-dragger) {
    padding: 10px 0;
  }

  :deep(.el-upload-dragger .el-icon--upload) {
    color: #2668b4;
    font-size: 45px;
    margin-bottom: 0;
  }

  :deep(.el-upload-list) {
    margin: 0 !important;
  }

  :deep(.el-button--small.is-round) {
    width: 24px;
    height: 24px;
    font-size: 14px;
    padding: 0;
  }

  .legend {
    //position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }

  .circle {
    background-color: #c6a5f4;
    border-radius: 50%;
  }

  .legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div {
      margin-right: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  h4 {
    text-align: center;
    font-size: 1.1rem;
    margin-right: 20px;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }

  /* New styles for the updated form */
  .species-section {
    padding: 0 0 0 16px;
  }

  .species-warning {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 8px;
    line-height: 1.4;
  }

  .data-filter-section {
    padding: 0 4px 0 16px;
    flex: 1;
  }

  .diversity-group {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;
    background-color: #ffffff;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .group-header {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }

  .group-title {
    color: #1e7cb2;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .group-actions {
    display: flex;
  }

  .group-content {
    .el-form-item {
      margin-bottom: 12px;
    }
  }

  .upload-section {
    flex: 1;

    .upload-demo {
      margin-top: 8px;
    }
  }

  .mb-2 {
    margin-bottom: 12px !important;
  }

  .mb-3 {
    margin-bottom: 20px !important;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  :deep(.el-radio) {
    margin-right: 0;
  }

  /* Section label styling */
  .section-label {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1e7cb2;
    padding-bottom: 4px;
    margin-top: 22px !important;

    .el-icon {
      margin-right: 0.2rem;
    }
  }

  /* Custom radio button styling */
  .custom-radio-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    :deep(.el-radio-button) {
      margin-right: 0;
    }

    :deep(.el-radio-button__inner) {
      background-color: #f5f5f5;
      border: 1px solid #dcdfe6;
      color: #606266;
      padding: 4px 10px !important;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e6f7ff;
        border-color: #3498db;
        color: #3498db;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background-color: #e6f7ff;
      border-color: #3498db;
      color: #3498db;
      box-shadow: none;
    }

    :deep(.el-radio-button:first-child .el-radio-button__inner) {
      border-radius: 4px;
    }

    :deep(.el-radio-button:last-child .el-radio-button__inner) {
      border-radius: 4px;
    }
  }

  /* Group title input styling */
  .group-title-input {
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: none;
      box-shadow: none;
      padding: 0;
    }

    :deep(.el-input__inner) {
      color: #1e7cb2;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
    }
  }

  /* 选中数据信息样式 */
  .selected-data-info {
    margin-top: 8px;
    padding-left: 0;

    .el-tag {
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  /* map */
  .chart-card {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #272728;
    background: rgba(255, 255, 255, 0.9);
    border-left: 1px solid #007ed31a;
    width: auto;
    min-width: 280px;
    padding: 12px;
    border-radius: 8px;
    z-index: 999;
    font-size: 14px;

    // 样本信息样式
    .sample-info {
      margin-bottom: 4px;

      .sample-count {
        color: #0080b0;
        font-weight: 600;
        margin-left: 4px;
      }
    }

    // 图例容器样式
    .legend-container {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 8px;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px dashed #999;
    }

    // 图例项样式
    .legend-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .size-label {
        text-align: center;
        font-weight: 500;
        white-space: nowrap;
        line-height: 1;
      }

      .circle {
        background: #c3aaf1;
        border-radius: 50%;
      }
    }

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .link-style {
    color: #3498db;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      color: #2980b9;
      text-decoration: underline;
    }

    //&:visited {
    //  color: #8e44ad;
    //}
  }
</style>
